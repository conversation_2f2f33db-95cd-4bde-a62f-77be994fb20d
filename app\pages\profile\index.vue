<template>
  <div class="min-h-screen bg-theme-dark text-white-force" dir="rtl">
    <!-- Mobile-First Header -->
    <div class="sticky top-0 z-20 bg-theme-glass backdrop-blur-md border-b border-theme-light px-4 py-4">
      <div class="flex items-center justify-between">
        <h1 class="text-white-force text-xl font-bold">الملف الشخصي</h1>
        <button @click="showQuickActions = !showQuickActions" class="btn-secondary p-2">
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"></path>
          </svg>
        </button>
      </div>
    </div>

    <div class="px-4 py-6 space-y-6">
      <!-- Profile Summary Card - Mobile Optimized -->
      <div class="bg-gradient-to-br from-purple-600/20 to-pink-600/20 backdrop-blur-md border border-theme-light rounded-3xl p-6 shadow-2xl">
        <div class="flex items-center space-x-4 space-x-reverse mb-6">
          <div class="relative">
            <div class="w-16 h-16 bg-gradient-to-r from-purple-600 to-pink-600 rounded-2xl flex items-center justify-center shadow-lg">
              <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
              </svg>
            </div>
            <button class="absolute -bottom-1 -right-1 w-6 h-6 bg-theme-accent rounded-full flex items-center justify-center hover:scale-110 transition-transform">
              <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
              </svg>
            </button>
          </div>
          <div class="flex-1">
            <h2 class="text-white-force text-lg font-bold mb-1">{{ userProfile.name }}</h2>
            <p class="text-theme-muted text-sm mb-2">{{ userProfile.email }}</p>
            <div class="inline-flex items-center px-3 py-1 bg-green-500/20 text-green-400 rounded-full text-xs font-medium">
              <div class="w-2 h-2 bg-green-400 rounded-full ml-2"></div>
              حساب مفعل
            </div>
          </div>
        </div>

        <!-- Quick Stats -->
        <div class="grid grid-cols-2 gap-4">
          <div class="bg-theme-surface/20 rounded-xl p-4 text-center">
            <div class="text-white-force text-xl font-bold">{{ userProfile.totalOrders }}</div>
            <div class="text-theme-muted text-sm">إجمالي الطلبات</div>
          </div>
          <div class="bg-theme-surface/20 rounded-xl p-4 text-center">
            <div class="text-white-force text-xl font-bold">{{ userProfile.memberSince }}</div>
            <div class="text-theme-muted text-sm">عضو منذ</div>
          </div>
        </div>
      </div>

      <!-- Tab Navigation -->
      <div class="bg-theme-glass backdrop-blur-md border border-theme-light rounded-2xl p-2 shadow-lg">
        <div class="grid grid-cols-3 gap-2">
          <button
            @click="activeTab = 'personal'"
            :class="[
              'flex flex-col items-center p-3 rounded-xl transition-all duration-200',
              activeTab === 'personal'
                ? 'bg-gradient-to-r from-purple-600 to-pink-600 text-white shadow-lg'
                : 'text-theme-muted hover:text-white-force hover:bg-theme-surface/20'
            ]"
          >
            <svg class="w-6 h-6 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
            </svg>
            <span class="text-xs font-medium">المعلومات</span>
          </button>
          <button
            @click="activeTab = 'settings'"
            :class="[
              'flex flex-col items-center p-3 rounded-xl transition-all duration-200',
              activeTab === 'settings'
                ? 'bg-gradient-to-r from-purple-600 to-pink-600 text-white shadow-lg'
                : 'text-theme-muted hover:text-white-force hover:bg-theme-surface/20'
            ]"
          >
            <svg class="w-6 h-6 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
            </svg>
            <span class="text-xs font-medium">الإعدادات</span>
          </button>
          <button
            @click="activeTab = 'security'"
            :class="[
              'flex flex-col items-center p-3 rounded-xl transition-all duration-200',
              activeTab === 'security'
                ? 'bg-gradient-to-r from-purple-600 to-pink-600 text-white shadow-lg'
                : 'text-theme-muted hover:text-white-force hover:bg-theme-surface/20'
            ]"
          >
            <svg class="w-6 h-6 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
            </svg>
            <span class="text-xs font-medium">الأمان</span>
          </button>
        </div>
      </div>

      <!-- Tab Content -->
      <div class="bg-theme-glass backdrop-blur-md border border-theme-light rounded-3xl p-6 shadow-xl">
        <!-- Personal Information Tab -->
        <div v-if="activeTab === 'personal'" class="space-y-6">
          <h3 class="text-white-force text-xl font-bold mb-6">المعلومات الشخصية</h3>
          <form @submit.prevent="updateProfile" class="space-y-4">
            <!-- Name Fields -->
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <label class="block text-white-force text-sm font-medium mb-2">الاسم الأول</label>
                <input v-model="profileForm.firstName" type="text" class="input-field" placeholder="أدخل الاسم الأول" dir="rtl">
              </div>
              <div>
                <label class="block text-white-force text-sm font-medium mb-2">الاسم الأخير</label>
                <input v-model="profileForm.lastName" type="text" class="input-field" placeholder="أدخل الاسم الأخير" dir="rtl">
              </div>
            </div>

            <!-- Email -->
            <div>
              <label class="block text-white-force text-sm font-medium mb-2">البريد الإلكتروني</label>
              <input v-model="profileForm.email" type="email" class="input-field" placeholder="أدخل البريد الإلكتروني" dir="rtl">
            </div>

            <!-- Phone -->
            <div>
              <label class="block text-white-force text-sm font-medium mb-2">رقم الهاتف</label>
              <input v-model="profileForm.phone" type="tel" class="input-field" placeholder="أدخل رقم الهاتف" dir="rtl">
            </div>

            <!-- Favorite Gaming Platform -->
            <div>
              <label class="block text-white-force text-sm font-medium mb-2">منصة الألعاب المفضلة</label>
              <select v-model="profileForm.favoritePlatform" class="input-field" dir="rtl">
                <option value="">اختر المنصة</option>
                <option value="PC">الكمبيوتر الشخصي</option>
                <option value="PlayStation">بلايستيشن</option>
                <option value="Xbox">إكس بوكس</option>
                <option value="Nintendo">نينتندو</option>
                <option value="Mobile">الهاتف المحمول</option>
                <option value="Steam">ستيم</option>
                <option value="Epic">إيبك جيمز</option>
              </select>
            </div>

            <!-- Submit Button -->
            <div class="pt-4">
              <button type="submit" class="btn-primary w-full py-3 font-bold hover:scale-105 transition-all duration-200">
                حفظ التغييرات
              </button>
            </div>
          </form>
        </div>

        <!-- Settings Tab -->
        <div v-if="activeTab === 'settings'" class="space-y-4">
          <h3 class="text-white-force text-xl font-bold mb-6">إعدادات الحساب</h3>

          <!-- Gaming Preferences -->
          <div class="flex items-center justify-between p-4 bg-gradient-to-r from-purple-500/10 to-purple-600/10 border border-purple-500/20 rounded-2xl">
            <div class="flex items-center space-x-3 space-x-reverse">
              <div class="w-12 h-12 bg-purple-500/20 rounded-xl flex items-center justify-center">
                <svg class="w-6 h-6 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                </svg>
              </div>
              <div>
                <div class="text-white-force font-bold">تفضيلات الألعاب</div>
                <div class="text-theme-muted text-sm">إعداد الألعاب المفضلة والإشعارات</div>
              </div>
            </div>
            <button class="btn-secondary px-4 py-2 text-sm font-medium">
              إعداد
            </button>
          </div>

          <!-- Email Notifications -->
          <div class="flex items-center justify-between p-4 bg-gradient-to-r from-blue-500/10 to-blue-600/10 border border-blue-500/20 rounded-2xl">
            <div class="flex items-center space-x-3 space-x-reverse">
              <div class="w-12 h-12 bg-blue-500/20 rounded-xl flex items-center justify-center">
                <svg class="w-6 h-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                </svg>
              </div>
              <div>
                <div class="text-white-force font-bold">إشعارات البريد الإلكتروني</div>
                <div class="text-theme-muted text-sm">تلقي العروض والتحديثات</div>
              </div>
            </div>
            <label class="relative inline-flex items-center cursor-pointer">
              <input v-model="accountSettings.emailNotifications" type="checkbox" class="sr-only peer">
              <div class="w-12 h-6 bg-theme-surface peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-gradient-to-r peer-checked:from-purple-600 peer-checked:to-pink-600"></div>
            </label>
          </div>

          <!-- Wishlist Notifications -->
          <div class="flex items-center justify-between p-4 bg-gradient-to-r from-pink-500/10 to-pink-600/10 border border-pink-500/20 rounded-2xl">
            <div class="flex items-center space-x-3 space-x-reverse">
              <div class="w-12 h-12 bg-pink-500/20 rounded-xl flex items-center justify-center">
                <svg class="w-6 h-6 text-pink-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                </svg>
              </div>
              <div>
                <div class="text-white-force font-bold">إشعارات قائمة الأمنيات</div>
                <div class="text-theme-muted text-sm">تنبيهات عند توفر الألعاب المفضلة</div>
              </div>
            </div>
            <label class="relative inline-flex items-center cursor-pointer">
              <input v-model="accountSettings.wishlistNotifications" type="checkbox" class="sr-only peer">
              <div class="w-12 h-6 bg-theme-surface peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-gradient-to-r peer-checked:from-purple-600 peer-checked:to-pink-600"></div>
            </label>
          </div>
        </div>

        <!-- Security Tab -->
        <div v-if="activeTab === 'security'" class="space-y-4">
          <h3 class="text-white-force text-xl font-bold mb-6">الأمان والحماية</h3>

          <!-- Change Password -->
          <div class="flex items-center justify-between p-4 bg-gradient-to-r from-blue-500/10 to-blue-600/10 border border-blue-500/20 rounded-2xl">
            <div class="flex items-center space-x-3 space-x-reverse">
              <div class="w-12 h-12 bg-blue-500/20 rounded-xl flex items-center justify-center">
                <svg class="w-6 h-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                </svg>
              </div>
              <div>
                <div class="text-white-force font-bold">تغيير كلمة المرور</div>
                <div class="text-theme-muted text-sm">آخر تغيير منذ 3 أشهر</div>
              </div>
            </div>
            <button class="btn-secondary px-4 py-2 text-sm font-medium">
              تغيير
            </button>
          </div>

          <!-- Two Factor Authentication -->
          <div class="flex items-center justify-between p-4 bg-gradient-to-r from-green-500/10 to-green-600/10 border border-green-500/20 rounded-2xl">
            <div class="flex items-center space-x-3 space-x-reverse">
              <div class="w-12 h-12 bg-green-500/20 rounded-xl flex items-center justify-center">
                <svg class="w-6 h-6 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                </svg>
              </div>
              <div>
                <div class="text-white-force font-bold">المصادقة الثنائية</div>
                <div class="text-theme-muted text-sm">حماية إضافية لحسابك</div>
              </div>
            </div>
            <label class="relative inline-flex items-center cursor-pointer">
              <input v-model="securitySettings.twoFactorEnabled" type="checkbox" class="sr-only peer">
              <div class="w-12 h-6 bg-theme-surface peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-gradient-to-r peer-checked:from-purple-600 peer-checked:to-pink-600"></div>
            </label>
          </div>

          <!-- Auto-Purchase Settings -->
          <div class="flex items-center justify-between p-4 bg-gradient-to-r from-green-500/10 to-green-600/10 border border-green-500/20 rounded-2xl">
            <div class="flex items-center space-x-3 space-x-reverse">
              <div class="w-12 h-12 bg-green-500/20 rounded-xl flex items-center justify-center">
                <svg class="w-6 h-6 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
              <div>
                <div class="text-white-force font-bold">الشراء التلقائي</div>
                <div class="text-theme-muted text-sm">تفعيل الشراء التلقائي للألعاب المفضلة</div>
              </div>
            </div>
            <label class="relative inline-flex items-center cursor-pointer">
              <input v-model="securitySettings.autoPurchase" type="checkbox" class="sr-only peer">
              <div class="w-12 h-6 bg-theme-surface peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-gradient-to-r peer-checked:from-purple-600 peer-checked:to-pink-600"></div>
            </label>
          </div>

          <!-- Gaming Library Sync -->
          <div class="flex items-center justify-between p-4 bg-gradient-to-r from-blue-500/10 to-blue-600/10 border border-blue-500/20 rounded-2xl">
            <div class="flex items-center space-x-3 space-x-reverse">
              <div class="w-12 h-12 bg-blue-500/20 rounded-xl flex items-center justify-center">
                <svg class="w-6 h-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                </svg>
              </div>
              <div>
                <div class="text-white-force font-bold">مزامنة مكتبة الألعاب</div>
                <div class="text-theme-muted text-sm">ربط حساباتك من منصات أخرى</div>
              </div>
            </div>
            <button class="btn-secondary px-4 py-2 text-sm font-medium">
              ربط الحسابات
            </button>
          </div>
        </div>
      </div>

      <!-- Quick Actions Floating Menu -->
      <div v-if="showQuickActions" class="fixed bottom-20 right-4 bg-theme-glass backdrop-blur-md border border-theme-light rounded-2xl p-4 shadow-2xl z-30">
        <div class="space-y-3">
          <button @click="navigateTo('/wallet')" class="flex items-center space-x-3 space-x-reverse w-full p-3 text-right hover:bg-theme-surface/20 rounded-xl transition-all">
            <svg class="w-5 h-5 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
            </svg>
            <span class="text-white-force font-medium">المحفظة</span>
          </button>
          <button @click="navigateTo('/orders')" class="flex items-center space-x-3 space-x-reverse w-full p-3 text-right hover:bg-theme-surface/20 rounded-xl transition-all">
            <svg class="w-5 h-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
            </svg>
            <span class="text-white-force font-medium">الطلبات</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// Middleware
definePageMeta({
  middleware: 'auth'
})

// SEO Meta
useSeoMeta({
  title: 'الملف الشخصي - بنتاكون',
  description: 'إدارة معلوماتك الشخصية وإعدادات الحساب في متجر بنتاكون',
})

// Auth store
const { user } = useAuthStore()

// Reactive state
const activeTab = ref('personal')
const showQuickActions = ref(false)

const userProfile = computed(() => ({
  name: user?.name || 'مستخدم',
  email: user?.email || '',
  totalOrders: 15,
  memberSince: '2024'
}))

const profileForm = ref({
  firstName: user?.name?.split(' ')[0] || '',
  lastName: user?.name?.split(' ')[1] || '',
  email: user?.email || '',
  phone: '+************',
  country: 'SA'
})

const securitySettings = ref({
  twoFactorEnabled: true,
  loginNotifications: true
})

const accountSettings = ref({
  emailNotifications: true,
  smsNotifications: false
})

// Methods
const updateProfile = () => {
  // Handle profile update
  console.log('Profile updated:', profileForm.value)
}

// Close quick actions when clicking outside
onMounted(() => {
  document.addEventListener('click', (e) => {
    if (!e.target?.closest('.fixed.bottom-20')) {
      showQuickActions.value = false
    }
  })
})
</script>
